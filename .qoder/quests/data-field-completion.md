# TestDataBiz SDK 数据字段完善设计

## 1. 概述

### 1.1 项目背景
TestDataBiz SDK是为RDC（Report Data Center）提供数据转换服务的工具包。当前SDK中的数据传输对象（DTO）缺少部分数据库表字段，导致数据转换时信息丢失，影响数据完整性。

### 1.2 设计目标
- 根据data.md中定义的数据库表字段，完善SDK中Input和Output DTO对象
- 确保数据转换过程中所有必要字段的正确映射
- 维护SDK的向后兼容性
- 提升数据转换的完整性和准确性

### 1.3 核心价值
- **数据完整性**：确保所有数据库字段在SDK转换过程中不丢失
- **映射准确性**：建立完整的字段映射关系
- **向后兼容**：保证现有功能不受影响
- **扩展性**：为未来新增字段提供标准化处理方式

## 2. SDK架构分析

### 2.1 整体架构图

```mermaid
graph TB
    A[外部系统] -->|输入数据| B[RdConvertUtil]
    B --> C[DataConvertHandler]
    C --> D[Input DTO Objects]
    D --> E[转换逻辑]
    E --> F[Output DTO Objects]
    F --> G[RdReportDataDTO]
    G -->|转换结果| H[目标系统]
    
    subgraph "Input DTOs"
        D1[RdAttachmentInput]
        D2[RdCustomerInput]
        D3[RdQuotationInput]
        D4[RdReportInput]
        D5[其他Input DTOs]
    end
    
    subgraph "Output DTOs"
        F1[RdAttachmentDTO]
        F2[RdCustomerDTO]
        F3[RdQuotationDTO]
        F4[RdReportDTO]
        F5[其他Output DTOs]
    end
    
    D --> D1
    D --> D2
    D --> D3
    D --> D4
    D --> D5
    
    F --> F1
    F --> F2
    F --> F3
    F --> F4
    F --> F5
```

### 2.2 核心组件说明

| 组件 | 职责 | 关键类 |
|------|------|--------|
| 转换入口 | 提供统一的数据转换接口 | `RdConvertUtil` |
| 转换处理器 | 实现具体的转换逻辑 | `AbstractDataConvertHandler`, `DataConvertV1Handler` |
| 输入模型 | 定义输入数据结构 | `input.dto.*Input` |
| 输出模型 | 定义输出数据结构 | `output.dto.*DTO` |
| 工具类 | 提供转换辅助功能 | `ReportDataMapper`, `ConvertJsonValueUtil` |

### 2.3 数据转换流程

```mermaid
sequenceDiagram
    participant Client as 外部客户端
    participant RdConvertUtil as RdConvertUtil
    participant Handler as DataConvertHandler
    participant InputDTO as Input DTOs
    participant OutputDTO as Output DTOs
    participant Mapper as 字段映射器
    
    Client->>RdConvertUtil: convert(requestJson, version, env)
    RdConvertUtil->>Handler: 解析输入数据
    Handler->>InputDTO: 创建Input对象
    InputDTO->>Mapper: 字段映射转换
    Mapper->>OutputDTO: 生成Output对象
    OutputDTO->>Handler: 返回转换结果
    Handler->>RdConvertUtil: 组装最终数据
    RdConvertUtil->>Client: 返回RdReportDataDTO
```

## 3. 数据模型缺失字段分析

### 3.1 字段缺失统计表

| 数据库表 | 现有DTO | 缺失字段数量 | 主要缺失字段 |
|----------|---------|-------------|-------------|
| tb_attachment | RdAttachmentInput/DTO | 2 | created_by, created_date |
| tb_customer | RdCustomerInput/DTO | 1 | created_date |
| tb_customer_lang | RdCustomerLangDTO | 3 | language_id, customer_name, customer_address |
| tb_quotation | RdQuotationInput/DTO | 2 | created_by, created_date |
| tb_quotation_lang | RdQuotationLangDTO | 6 | language_id, payer_customer_name, citation_name等 |
| tb_report_conclusion | RdReportConclusionDTO | 1 | created_date |
| tb_report_invoice | RdReportInvoiceDTO | 1 | created_date |
| tb_report_ext | RdReportExtDTO | 1 | request_json |
| tb_report_matrix | RdReportMatrixDTO | 3 | rd_object_rel_id, created_by, created_date |
| tb_report_matrix_lang | RdReportMatrixLangDTO | 8 | rd_report_matrix_id, language_id等 |
| tb_report_test_result | RdReportTestResultDTO | 3 | created_by, created_date, test_data |
| tb_report_test_result_lang | RdReportTestResultLangDTO | 7 | rd_report_test_result_id, language_id等 |
| tb_test_data_object_rel | RdTestDataObjectRelDTO | 12 | ProductLineCode, LabCode, OrderNo等 |
| tb_test_sample | RdTestSampleDTO | 2 | created_by, created_date |
| tb_test_sample_group | RdTestSampleGroupDTO | 1 | created_date |

### 3.2 关键字段详细分析

#### 3.2.1 附件表字段缺失

**tb_attachment 表缺失字段：**
- `created_by`: 创建者信息，数据类型：String
- `created_date`: 创建时间，数据类型：Date

**影响分析：**
- 无法追踪附件的创建者和创建时间
- 审计功能不完整
- 数据溯源能力受限

#### 3.2.2 客户相关表字段缺失

**tb_customer 表缺失字段：**
- `created_date`: 客户创建时间，数据类型：Date

**tb_customer_lang 表缺失字段：**
- `language_id`: 语言ID，数据类型：Integer
- `customer_name`: 客户名称（多语言），数据类型：String
- `customer_address`: 客户地址（多语言），数据类型：String

**影响分析：**
- 多语言客户信息处理不完整
- 客户数据时间戳缺失
- 国际化功能受限

#### 3.2.3 测试数据对象关联表字段缺失

**tb_test_data_object_rel 表缺失字段：**
- `ProductLineCode`: 产品线代码，数据类型：String
- `LabCode`: 实验室代码，数据类型：String
- `OrderNo`: 订单号，数据类型：String
- `ParentOrderNo`: 父订单号，数据类型：String
- `ReportNo`: 报告号，数据类型：String
- `ObjectNo`: 对象编号，数据类型：String
- `ExternalNo`: 外部编号，数据类型：String
- `ExternalObjectNo`: 外部对象编号，数据类型：String
- `LanguageId`: 语言ID，数据类型：Integer
- `CompleteDate`: 完成日期，数据类型：Date
- `ActiveIndicator`: 激活标识，数据类型：Integer
- `CreatedDate`: 创建日期，数据类型：Date
- `source_type_label`: 数据源类型标签，数据类型：String

**影响分析：**
- 测试数据对象关联信息不完整
- 数据溯源和追踪功能受限
- 多语言支持不完整

## 4. 字段映射策略

### 4.1 字段命名规范

| 数据库字段 | Java字段 | 转换规则 |
|------------|----------|----------|
| created_by | createdBy | 下划线转驼峰 |
| created_date | createdDate | 下划线转驼峰，Date类型 |
| language_id | languageId | 下划线转驼峰，Integer类型 |
| rd_object_rel_id | rdObjectRelId | 下划线转驼峰，Long类型 |
| active_indicator | activeIndicator | 下划线转驼峰，Integer类型 |

### 4.2 数据类型映射

```mermaid
graph LR
    A[数据库类型] --> B[Java类型]
    
    A1[VARCHAR] --> B1[String]
    A2[DATETIME] --> B2[Date]
    A3[INT] --> B3[Integer]
    A4[BIGINT] --> B4[Long]
    A5[TEXT] --> B5[String]
    A6[TINYINT] --> B6[Integer]
    A7[DECIMAL] --> B7[BigDecimal]
```

### 4.3 字段映射关系图

```mermaid
graph TB
    subgraph "数据库表"
        T1[tb_attachment<br/>created_by, created_date]
        T2[tb_customer<br/>created_date]
        T3[tb_test_data_object_rel<br/>ProductLineCode, LabCode等]
    end
    
    subgraph "Input DTOs"
        I1[RdAttachmentInput<br/>+createdBy, +createdDate]
        I2[RdCustomerInput<br/>+createdDate]
        I3[RdTestDataObjectRelInput<br/>+productLineCode, +labCode等]
    end
    
    subgraph "Output DTOs"
        O1[RdAttachmentDTO<br/>+createdBy, +createdDate]
        O2[RdCustomerDTO<br/>+createdDate]
        O3[RdTestDataObjectRelDTO<br/>+productLineCode, +labCode等]
    end
    
    T1 -.-> I1
    T2 -.-> I2
    T3 -.-> I3
    
    I1 --> O1
    I2 --> O2
    I3 --> O3
```

## 5. 实施方案

### 5.1 实施阶段规划

| 阶段 | 任务 | 涉及组件 | 预估工作量 |
|------|------|----------|------------|
| 阶段1 | 补充核心DTO字段 | Input/Output DTOs | 2天 |
| 阶段2 | 更新转换逻辑 | AbstractDataConvertHandler及其子类 | 3天 |
| 阶段3 | 完善工具类 | Mapper、Util类 | 1天 |
| 阶段4 | 测试验证 | 单元测试、集成测试 | 2天 |
| 阶段5 | 文档更新 | SDK文档、映射文档 | 1天 |

### 5.2 详细实施步骤

#### 5.2.1 DTO字段补充

**步骤1：更新Input DTO对象**

需要更新的Input DTO类：
- `RdAttachmentInput`：增加createdBy, createdDate字段
- `RdCustomerInput`：增加createdDate字段  
- `RdQuotationInput`：增加createdBy, createdDate字段
- `RdReportMatrixInput`：增加rdObjectRelId, createdBy, createdDate字段
- `RdTestSampleInput`：增加createdBy, createdDate字段
- 其他相关Input类

**步骤2：更新Output DTO对象**

对应的Output DTO类也需要同步更新字段，确保Input到Output的完整映射。

#### 5.2.2 转换逻辑更新

**核心方法更新：**

```java
// AbstractDataConvertHandler中需要更新的方法
protected void convertAttachmentList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
    // 现有逻辑保持不变
    // 新增字段映射逻辑
    rdAttachmentDTOS.forEach(attachment -> {
        // 设置创建者信息
        if (StringUtils.isNotBlank(attachment.getCreatedBy())) {
            attachment.setCreatedBy(attachment.getCreatedBy());
        }
        // 设置创建时间
        if (attachment.getCreatedDate() != null) {
            attachment.setCreatedDate(attachment.getCreatedDate());
        }
    });
}
```

#### 5.2.3 工具类增强

**ReportDataMapper类更新：**
- 增加新字段的映射方法
- 提供字段验证功能
- 支持批量字段映射

### 5.3 向后兼容性保证

| 兼容性措施 | 实施方式 | 影响评估 |
|------------|----------|----------|
| 字段默认值 | 新增字段提供合理默认值 | 低风险 |
| 可选字段 | 新增字段设为可选，不影响现有流程 | 无影响 |
| 版本控制 | 通过version参数控制字段处理逻辑 | 低风险 |
| 渐进式更新 | 分批次更新，先更新非关键字段 | 低风险 |

## 6. 关键字段处理策略

### 6.1 时间字段处理

**created_date / created_by 字段：**
- 数据来源：原始输入数据或系统当前时间
- 格式标准：Date类型，支持时区转换
- 默认值策略：如果输入为空，使用当前时间

### 6.2 多语言字段处理

**language_id 相关字段：**
- 数据来源：输入数据中的语言标识
- 验证规则：必须是系统支持的语言ID
- 默认值：使用系统默认语言（通常为英语）

### 6.3 关联ID字段处理

**rd_object_rel_id / rd_report_matrix_id 等：**
- 数据来源：通过对象关联关系计算
- 生成策略：UUID或序列生成
- 验证规则：确保关联对象存在

### 6.4 状态标识字段

**active_indicator 字段：**
- 数据来源：业务逻辑判断
- 取值范围：0(非激活), 1(激活)
- 默认值：1(激活状态)

## 7. 测试策略

### 7.1 单元测试

**测试范围：**
- 所有新增字段的getter/setter方法
- 字段映射逻辑的正确性
- 数据类型转换的准确性
- 默认值设置的合理性

**测试用例设计：**

| 测试场景 | 输入数据 | 期望输出 | 验证要点 |
|----------|----------|----------|----------|
| 完整字段映射 | 包含所有字段的输入 | 所有字段正确映射 | 字段值准确性 |
| 部分字段缺失 | 缺少新增字段的输入 | 使用默认值填充 | 默认值正确性 |
| 空值处理 | 字段值为null的输入 | 正确处理空值 | 空值安全性 |
| 数据类型转换 | 不同类型的输入数据 | 正确的类型转换 | 类型安全性 |

### 7.2 集成测试

**测试目标：**
- 验证完整的数据转换流程
- 确保与现有功能的兼容性
- 验证性能影响

**测试数据：**
- 使用真实的生产数据样本
- 覆盖各种数据场景
- 包含边界值测试

### 7.3 回归测试

**回归测试范围：**
- 现有的所有SDK功能
- 数据转换的完整性
- 性能基准对比

**测试自动化：**
- 集成到CI/CD流程
- 自动化测试报告
- 性能监控告警