package com.sgs.testdatabiz.domain.service.testdata.model;

import java.util.Objects;

import org.apache.commons.codec.digest.DigestUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import lombok.Data;
/**
 * 业务版本ID值对象
 * 封装MD5生成逻辑，用于生成业务对象的唯一标识
 * 
 * <AUTHOR>
 */
@Data
public class BizVersionId {
    
    private final String value;
    
    /**
     * 私有构造函数
     */
    private BizVersionId(String value) {
        this.value = value;
    }
    
    /**
     * 从TestDataObjectRelPO生成BizVersionId
     * 
     * @param objectRel 测试数据对象关系PO
     * @return BizVersionId实例
     */
    public static BizVersionId fromObjectRel(TestDataObjectRelPO objectRel) {
        if (objectRel == null) {
            throw new IllegalArgumentException("TestDataObjectRelPO cannot be null");
        }
        
        // 不需要创建副本，因为我们不修改原始对象
        // TestDataObjectRelPO copy = createObjectRelCopy(objectRel);
        
        // 标准化字段值，使用空字符串替换null值，避免String.format的%字符异常
        String productLineCode = emptyIfNull(objectRel.getProductLineCode());
        String labCode = emptyIfNull(objectRel.getLabCode());
        String orderNo = emptyIfNull(objectRel.getOrderNo());
        String parentOrderNo = emptyIfNull(objectRel.getParentOrderNo());
        String reportNo = emptyIfNull(objectRel.getReportNo());
        String objectNo = emptyIfNull(objectRel.getObjectNo());
        String externalId = emptyIfNull(objectRel.getExternalId());
        String externalNo = emptyIfNull(objectRel.getExternalNo());
        String externalObjectNo = emptyIfNull(objectRel.getExternalObjectNo());
        String languageId = String.valueOf(objectRel.getLanguageId() != null ? objectRel.getLanguageId() : 0);
        String sourceType = String.valueOf(objectRel.getSourceType() != null ? objectRel.getSourceType() : 0);
        String completeDate = String.valueOf(objectRel.getCompleteDate() != null ? objectRel.getCompleteDate() : "");
        String excludeCustomerInterface = emptyIfNull(objectRel.getExcludeCustomerInterface());
        
       
        StringBuffer md5Input = new StringBuffer();
        md5Input.append(productLineCode)
                .append(labCode)
                .append(orderNo)
                .append(parentOrderNo)
                .append(reportNo)
                .append(objectNo)
                .append(externalId)
                .append(externalNo)
                .append(externalObjectNo)
                .append(languageId)
                .append(sourceType)
                .append(completeDate)
                .append(excludeCustomerInterface);
        
        return new BizVersionId(DigestUtils.md5Hex(md5Input.toString()));
    }
    
    /**
     * 从TestDataMatrixInfoPO生成BizVersionId
     * 
     * @param matrix 测试数据矩阵信息PO
     * @return BizVersionId实例
     */
    public static BizVersionId fromMatrix(TestDataMatrixInfoPO matrix) {
        if (matrix == null) {
            throw new IllegalArgumentException("TestDataMatrixInfoPO cannot be null");
        }
        
        // 不需要创建副本，因为我们不修改原始对象
        // TestDataMatrixInfoPO copy = createMatrixCopy(matrix);
        
        // 标准化字段值，使用空字符串替换null值，避免String.format的%字符异常
        String objectRelId = emptyIfNull(matrix.getObjectRelId());
        String testMatrixId = emptyIfNull(matrix.getTestMatrixId());
        String testLineMappingId = String.valueOf(NumberUtil.toInt(matrix.getTestLineMappingId()));
        String externalId = emptyIfNull(matrix.getExternalId());
        String externalCode = emptyIfNull(matrix.getExternalCode());
        String ppVersionId = String.valueOf(NumberUtil.toInt(matrix.getPpVersionId()));
        String aid = String.valueOf(NumberUtil.toLong(matrix.getAid()));
        String testLineId = String.valueOf(NumberUtil.toInt(matrix.getTestLineId()));
        String citationId = String.valueOf(NumberUtil.toInt(matrix.getCitationId()));
        String citationVersionId = String.valueOf(NumberUtil.toInt(matrix.getCitationVersionId()));
        String citationType = String.valueOf(NumberUtil.toInt(matrix.getCitationType()));
        String citationName = emptyIfNull(matrix.getCitationName());
        String sampleId = emptyIfNull(matrix.getSampleId());
        String testLineInstanceId = emptyIfNull(matrix.getTestLineInstanceId());
        String matrixSource = emptyIfNull(matrix.getMatrixSource());
        String sampleNo = emptyIfNull(matrix.getSampleNo());
        String externalSampleNo = emptyIfNull(matrix.getExternalSampleNo());
        String testLineSeq = String.valueOf(matrix.getTestLineSeq() != null ? matrix.getTestLineSeq() : 0);
        String sampleSeq = String.valueOf(matrix.getSampleSeq() != null ? matrix.getSampleSeq() : 0);
        String condition = emptyIfNull(matrix.getCondition());
        String evaluationAlias = emptyIfNull(matrix.getEvaluationAlias());
        String methodDesc = emptyIfNull(matrix.getMethodDesc());
        String conclusionId = emptyIfNull(matrix.getConclusionId());
        String conclusionDisplay = emptyIfNull(matrix.getConclusionDisplay());
        String extFields = emptyIfNull(matrix.getExtFields());
        
        StringBuffer md5Input = new StringBuffer();
        md5Input.append(objectRelId)
                .append(testMatrixId)
                .append(testLineMappingId)
                .append(externalId)
                .append(externalCode)
                .append(ppVersionId)
                .append(aid)
                .append(testLineId)
                .append(citationId)
                .append(citationVersionId)
                .append(citationType)
                .append(citationName)
                .append(sampleId)
                .append(testLineInstanceId)
                .append(matrixSource)
                .append(sampleNo)
                .append(externalSampleNo)
                .append(testLineSeq)
                .append(sampleSeq)
                .append(condition)
                .append(evaluationAlias)
                .append(methodDesc)
                .append(conclusionId)
                .append(conclusionDisplay)
                .append(extFields);

        
        return new BizVersionId(DigestUtils.md5Hex(md5Input.toString()));
    }
    
    /**
     * 从TestDataInfoPO生成BizVersionId
     * 
     * @param testData 测试数据信息PO
     * @return BizVersionId实例
     */
    public static BizVersionId fromTestData(TestDataInfoPO testData) {
        if (testData == null) {
            throw new IllegalArgumentException("TestDataInfoPO cannot be null");
        }
        
        // 不需要创建副本，因为我们不修改原始对象
        // TestDataInfoPO copy = createTestDataCopy(testData);
        
        // 标准化字段值，使用空字符串替换null值，避免String.format的%字符异常
        String objectRelId = emptyIfNull(testData.getObjectRelId());
        String testMatrixDataId = String.valueOf((NumberUtil.toLong(testData.getTestDataMatrixId())));
        String testMatrixId = emptyIfNull(testData.getTestMatrixId());
        String analyteCode = emptyIfNull(testData.getAnalyteCode());
        String analyteId = emptyIfNull(testData.getAnalyteId());
        String analyteName = emptyIfNull(testData.getAnalyteName());
        String analyteType = String.valueOf(NumberUtil.toInt(testData.getAnalyteType()));
        String analyteSeq = String.valueOf(NumberUtil.toInt(testData.getAnalyteSeq()));
        String reportUnit = emptyIfNull(testData.getReportUnit());
        String limitUnit = emptyIfNull(testData.getLimitUnit());
        String testValue = emptyIfNull(testData.getTestValue());
        String casNo = emptyIfNull(testData.getCasNo());
        String reportLimit = emptyIfNull(testData.getReportLimit());
        String conclusionId = emptyIfNull(testData.getConclusionId());
        String testResultType = emptyIfNull(testData.getTestResultType());
        String testDataId = emptyIfNull(testData.getTestDataId());
        String position = emptyIfNull(testData.getPosition());

        // 使用%%代替%字符，防止String.format解析异常
        StringBuffer md5Input = new StringBuffer();
        md5Input.append(objectRelId)
                .append(testMatrixDataId)
                .append(testMatrixId)
                .append(analyteCode)
                .append(analyteId)
                .append(analyteName)
                .append(analyteType)
                .append(analyteSeq)
                .append(reportUnit)
                .append(limitUnit)
                .append(testValue)
                .append(casNo)
                .append(reportLimit)
                .append(conclusionId)
                .append(testResultType)
                .append(testDataId)
                .append(position);
        return new BizVersionId(DigestUtils.md5Hex(md5Input.toString()));
    }
    
    /**
     * 直接从字符串创建BizVersionId
     * 
     * @param value MD5值
     * @return BizVersionId实例
     */
    public static BizVersionId of(String value) {
        if (value == null) {
            throw new IllegalArgumentException("BizVersionId value cannot be null");
        }
        return new BizVersionId(value);
    }
    
    /**
     * 获取MD5值
     * 
     * @return MD5字符串
     */
    public String getValue() {
        return value;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BizVersionId that = (BizVersionId) o;
        return Objects.equals(value, that.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
    
    // 添加辅助方法，将null值转换为空字符串
    private static String emptyIfNull(String str) {
        return str == null ? "" : str;
    }
    
}