package com.sgs.testdatabiz.domain.service.testdata.service.matrix;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.testdatabiz.core.util.SnowflakeIdWorker;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.domain.service.testdata.factory.TestDataMatrixFactory;
import com.sgs.testdatabiz.domain.service.testdata.model.BizVersionId;
import com.sgs.testdatabiz.domain.service.testdata.repository.TestDataRepository;
import com.sgs.testdatabiz.domain.service.testdata.strategy.MatrixReconciliationStrategy;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixInfo;
import com.sgs.trimslocal.facade.model.enums.ProductLineTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 测试矩阵管理服务实现
 * 负责测试矩阵的构建、映射创建和数据协调
 * 
 * <AUTHOR>
 */
@Service
public class TestMatrixServiceImpl implements TestMatrixService {

    private static final Logger logger = LoggerFactory.getLogger(TestMatrixServiceImpl.class);

    @Autowired
    private TestDataMatrixFactory testDataMatrixFactory;

    @Autowired
    private TestDataRepository testDataRepository;

    @Autowired
    private SnowflakeIdWorker idWorker;

    @Autowired
    private MatrixReconciliationStrategy matrixReconciliationStrategy;

    @Override
    public List<TestDataMatrixInfoPO> buildTestMatrices(ReportTestDataInfo reportInfo, TestDataObjectRelPO objectRel) {
        if (reportInfo == null) {
            throw new IllegalArgumentException("ReportTestDataInfo cannot be null");
        }

        List<TestDataMatrixInfoPO> testDataMatrices = Lists.newArrayList();
        List<TestDataTestMatrixInfo> testMatrices = reportInfo.getTestMatrixs();

        if (testMatrices == null || testMatrices.isEmpty()) {
            logger.debug("No test matrices found in report info for objectRel: {}",
                    objectRel != null ? objectRel.getId() : "null");
            return testDataMatrices;
        }

        for (TestDataTestMatrixInfo matrixInfo : testMatrices) {
            TestDataMatrixInfoPO testMatrix = testDataMatrixFactory.createTestMatrix(objectRel, matrixInfo);

            // 2023-06-02 jingjing说除了SL的其他都走原逻辑
            // 对于非SL产品线，设置为活跃状态
            if (!Objects.equals(reportInfo.getProductLineCode(), ProductLineTypeEnum.SL.getProductLineAbbr())) {
                testMatrix.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
            }

            testDataMatrices.add(testMatrix);

            logger.debug("Built test matrix with ID: {} for objectRel: {}",
                    testMatrix.getId(), objectRel != null ? objectRel.getId() : "null");
        }

        logger.info("Built {} test matrices for report: {}", testDataMatrices.size(), reportInfo.getReportNo());
        return testDataMatrices;
    }

    @Override
    public Map<String, TestDataMatrixInfoPO> createMatrixMap(List<TestDataMatrixInfoPO> testMatrices) {
        if (testMatrices == null) {
            throw new IllegalArgumentException("Test matrices list cannot be null");
        }

        Map<String, TestDataMatrixInfoPO> matrixMap = Maps.newLinkedHashMap();

        for (TestDataMatrixInfoPO matrix : testMatrices) {
            if (matrix == null || StringUtils.isBlank(matrix.getBizVersionId())) {
                logger.warn("Skipping matrix with null or empty bizVersionId");
                continue;
            }

            matrixMap.put(matrix.getBizVersionId(), matrix);
        }

        logger.debug("Created matrix map with {} entries", matrixMap.size());
        return matrixMap;
    }

    @Override
    public void reconcileWithExistingMatrices(Map<String, TestDataMatrixInfoPO> matrixMap, String objectRelId,
            String suffix) {
        if (matrixMap == null) {
            throw new IllegalArgumentException("Matrix map cannot be null");
        }
        if (StringUtils.isBlank(objectRelId)) {
            throw new IllegalArgumentException("ObjectRelId cannot be blank");
        }

        // 查询数据库中已存在的矩阵
        List<TestDataMatrixInfoPO> existingMatrices = testDataRepository.findExistingMatrices(objectRelId, suffix);

        logger.debug("Found {} existing matrices in database for objectRelId: {}, suffix: {}",
                existingMatrices.size(), objectRelId, suffix);

        // 将Map转换为List以使用策略模式
        List<TestDataMatrixInfoPO> newMatrices = Lists.newArrayList(matrixMap.values());

        // 使用策略模式进行数据协调
        List<TestDataMatrixInfoPO> reconciledMatrices = matrixReconciliationStrategy.reconcile(objectRelId,suffix,newMatrices,
                existingMatrices);

        // 更新原始Map中的数据（主要是ID的更新）
        for (TestDataMatrixInfoPO reconciledMatrix : reconciledMatrices) {
            String bizVersionId = matrixReconciliationStrategy.getBizVersionId(reconciledMatrix);
            if (StringUtils.isNotBlank(bizVersionId) && matrixMap.containsKey(bizVersionId)) {
                // 更新Map中对应的矩阵对象
                matrixMap.put(bizVersionId, reconciledMatrix);
            }
        }
    }

    @Override
    public Map<String, TestDataMatrixInfoPO> handleDuplicateMatrices(List<TestDataMatrixInfoPO> testMatrices) {
        if (testMatrices == null) {
            throw new IllegalArgumentException("Test matrices list cannot be null");
        }

        Map<String, TestDataMatrixInfoPO> matrixMap = Maps.newLinkedHashMap();

        for (TestDataMatrixInfoPO matrix : testMatrices) {
            if (matrix == null || StringUtils.isBlank(matrix.getBizVersionId())) {
                logger.warn("Skipping matrix with null or empty bizVersionId");
                continue;
            }

            if (matrixMap.containsKey(matrix.getBizVersionId())) {
                // TODO 非SHEIN的订单会出现重复的Matrix，这里又需要存入DB，只能给一个临时的Matrix（DIG-8621）
                logger.warn("Found duplicate matrix with bizVersionId: {}, generating temporary ID",
                        matrix.getBizVersionId());

                matrix.setTestMatrixId(String.valueOf(idWorker.nextId()));
                matrix.setBizVersionId(BizVersionId.fromMatrix(matrix).getValue());

                logger.debug("Generated new temporary matrix ID: {} and bizVersionId: {}",
                        matrix.getTestMatrixId(), matrix.getBizVersionId());
            }

            matrixMap.put(matrix.getBizVersionId(), matrix);
        }

        logger.debug("Handled duplicate matrices, final map size: {}", matrixMap.size());
        return matrixMap;
    }
}