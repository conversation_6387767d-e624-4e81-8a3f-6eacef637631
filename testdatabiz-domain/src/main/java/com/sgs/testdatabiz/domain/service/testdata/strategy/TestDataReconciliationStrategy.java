package com.sgs.testdatabiz.domain.service.testdata.strategy;

import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.domain.service.testdata.model.BizVersionId;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 测试数据协调策略实现
 * 负责处理测试数据的协调逻辑
 * 
 * <AUTHOR>
 */
@Component
public class TestDataReconciliationStrategy implements DataReconciliationStrategy<TestDataInfoPO> {
    
    private static final Logger logger = LoggerFactory.getLogger(TestDataReconciliationStrategy.class);
    
    @Override
    public List<TestDataInfoPO> reconcile(String objectRelId,
                                          String suffix,List<TestDataInfoPO> newData, List<TestDataInfoPO> existingData) {
        logger.debug("开始协调测试数据，新数据数量: {}, 现有数据数量: {}", 
                newData != null ? newData.size() : 0, 
                existingData != null ? existingData.size() : 0);
        
        if (newData == null) {
            newData = Lists.newArrayList();
        }
        
        if (existingData == null || existingData.isEmpty()) {
            logger.debug("没有现有测试数据，直接返回新数据");
            return newData;
        }
        
        // 创建新数据的映射，便于快速查找
        Map<String, TestDataInfoPO> newDataMap = newData.stream()
                .filter(testData -> StringUtils.isNotBlank(getBizVersionId(testData)))
                .collect(Collectors.toMap(this::getBizVersionId, testData -> testData, (existing, replacement) -> replacement));
        
        List<TestDataInfoPO> reconciledData = Lists.newArrayList(newData);
        
        // 处理现有数据的协调逻辑
        for (TestDataInfoPO existingTestData : existingData) {
            String existingBizVersionId = getBizVersionId(existingTestData);
            
            if (StringUtils.isBlank(existingBizVersionId)) {
                logger.warn("现有测试数据的BizVersionId为空，跳过协调，测试数据ID: {}", existingTestData.getId());
                continue;
            }
            
            // 查找新数据中是否有匹配的记录
            TestDataInfoPO matchingNewData = findMatchingTestData(newData, existingTestData, existingBizVersionId);
            
            if (matchingNewData != null) {
                // 如果找到匹配的新数据，更新其ID为现有数据的ID（表示更新操作）
                updateIdFromExisting(matchingNewData, existingTestData);
                logger.debug("找到匹配的测试数据，将更新现有记录，ID: {}, 分析物代码: {}", 
                        existingTestData.getId(), existingTestData.getAnalyteCode());
            } else {
                // 如果没有找到匹配的新数据，将现有数据标记为无效
                markAsInactive(existingTestData);
                reconciledData.add(existingTestData);
                logger.debug("现有测试数据未找到匹配项，标记为无效，ID: {}, 分析物代码: {}", 
                        existingTestData.getId(), existingTestData.getAnalyteCode());
            }
        }
        
        logger.info("测试数据协调完成， objectRelId:{},suffix:{},最终数据数量: {}", objectRelId,suffix,reconciledData.size());
        return reconciledData;
    }
    
    @Override
    public String getBizVersionId(TestDataInfoPO data) {
        if (data == null) {
            return null;
        }
        
        // 如果已经有BizVersionId，直接返回；否则重新计算
        if (StringUtils.isNotBlank(data.getBizVersionId())) {
            return data.getBizVersionId();
        }
        
        return BizVersionId.fromTestData(data).getValue();
    }
    
    @Override
    public void markAsInactive(TestDataInfoPO data) {
        if (data != null) {
            data.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
        }
    }
    
    @Override
    public void updateIdFromExisting(TestDataInfoPO newData, TestDataInfoPO existingData) {
        if (newData != null && existingData != null) {
            newData.setId(existingData.getId());
        }
    }
    
    /**
     * 查找匹配的测试数据
     * 支持两种匹配方式：
     * 1. 通过重新计算的BizVersionId匹配
     * 2. 通过数据库中存储的BizVersionId匹配（QA可能直接修改DB数据）
     * 
     * @param newDataList 新数据列表
     * @param existingData 现有数据
     * @param existingBizVersionId 现有数据的BizVersionId
     * @return 匹配的新数据，如果没有找到则返回null
     */
    private TestDataInfoPO findMatchingTestData(List<TestDataInfoPO> newDataList, 
                                               TestDataInfoPO existingData, 
                                               String existingBizVersionId) {
        if (newDataList == null || newDataList.isEmpty()) {
            return null;
        }
        
        return newDataList.stream()
                .filter(newData -> {
                    String newBizVersionId = getBizVersionId(newData);
                    // 优先通过重新计算的BizVersionId匹配
                    if (StringUtils.equalsIgnoreCase(newBizVersionId, existingBizVersionId)) {
                        return true;
                    }
                    // 如果重新计算的不匹配，尝试通过数据库中存储的BizVersionId匹配
                    // QA可能直接修改DB数据，导致DB的BizVersionId与实际计算的MD5不一致
                    return StringUtils.equalsIgnoreCase(newBizVersionId, existingData.getBizVersionId());
                })
                .findFirst()
                .orElse(null);
    }
}