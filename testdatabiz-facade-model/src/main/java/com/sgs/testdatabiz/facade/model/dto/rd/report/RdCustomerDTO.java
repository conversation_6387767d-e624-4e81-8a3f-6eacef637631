/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.facade.model.dto.rd.report;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdCustomerDTO implements Serializable{
    @ApiModelProperty(value = "customerUsage",dataType = "integer", required = true)
    private Integer customerUsage;
    private Long bossNo;
    private String customerGroupCode;
    private String customerGroupName;
    @ApiModelProperty(value = "customerName",dataType = "string", required = true)
    private String customerName;
    private String customerAddress;
    private String marketSegmentCode;
    private String marketSegmentName;
    private String customerRefId;
    private List<RdCustomerLanguageDTO> languageList;
    private List<RdCustomerContactDTO> customerContactList;
    @ApiModelProperty(value = "customerInstanceId",dataType = "string", required = true)
    private String customerInstanceId;
    @ApiModelProperty(value = "lastModifiedTimestamp",dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "activeIndicator",dataType = "integer", required = true)
    private Integer activeIndicator;
    @ApiModelProperty(value = "createdDate",dataType = "date", required = false)
    private Date createdDate;
}
