/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.quotation;

import com.sgs.testdatabiz.facade.model.dto.rd.report.RdAttachmentDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdCustomerDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdServiceItemDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
public class RdQuotationRelationshipDTO implements Serializable {

    private RdQuotationRelParentDTO parent;

    private RdQuotationRelParallelDTO parallel;

    private RdQuotationRelChildrenDTO children;

    @Data
    public static class RdQuotationRelParentDTO implements Serializable {

        private RdQuotationRelParentOrderDTO order;

        private List<RdQuotationRelParentReportDTO> reportList;

        @Data
        public static class RdQuotationRelParentOrderDTO implements Serializable {
            private String orderId;

            private String orderNo;
            private String rootOrderNo;//logicOrderNo
        }

        @Data
        public static class RdQuotationRelParentReportDTO implements Serializable{

            private String reportId;

            private String reportNo;
        }
    }

    @Data
    public static class RdQuotationRelParallelDTO implements Serializable {

        private List<RdQuotationRelParallelOrderDTO> bossOrderList;

        @Data
        public static class RdQuotationRelParallelOrderDTO implements Serializable {
            private String bossOrderNo;

            private String refBossOrderNo;
        }
    }

    @Data
    public static class RdQuotationRelChildrenDTO implements Serializable {

        private List<RdQuotationRelInvoiceDTO> invoiceList;

        @Data
        public static class RdQuotationRelInvoiceDTO implements Serializable {
            private String invoiceId;
        }
    }
}
