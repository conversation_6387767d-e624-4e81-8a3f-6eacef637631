/**
 * Copyright 2023 json.cn
 */
package com.sgs.testdatabiz.facade.model.dto.rd.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 */
@Data
public class RdOrderDTO extends RdOrderHeaderDTO implements Serializable {


    @ApiModelProperty(value = "systemId", dataType = "integer", required = true)
    private Integer systemId;
    /**
     * @validation Unique
     */
    @ApiModelProperty(value = "orderId",dataType = "string", required = true)
    private String orderId;
    private String parentOrderNo;
    private String originalOrderNo;
    @ApiModelProperty(value = "orderStatus", dataType = "integer", required = true)
    private Integer orderStatus;
    @ApiModelProperty(value = "serviceType", dataType = "integer", required = true)
    private Integer serviceType;
    private String orderType;
    private Integer operationType;
    private Integer operationMode;
    private String productCategory;
    private String productSubCategory;
    private Integer groupId;
    private String idbLab;
    private Integer tat;
    private Long actualTat;
    @ApiModelProperty(value = "serviceStartDate", dataType = "date", required = true)
    private Date serviceStartDate;
    @ApiModelProperty(value = "testingStartDate", dataType = "date", required = true)
    private Date testingStartDate;
    @ApiModelProperty(value = "testingEndDate", dataType = "date", required = true)
    private Date testingEndDate;
    @ApiModelProperty(value = "serviceConfirmDate", dataType = "date", required = true)
    private Date serviceConfirmDate;
    private Date sampleReceiveDate;
    private Date cuttingExpectDueDate;
    private Date orderExpectDueDate;
    private Date jobExpectDueDate;
    private Date subcontractExpectDueDate;
    private Date reportExpectDueDate;
    @ApiModelProperty(value = "softCopyDeliveryDate", dataType = "date", required = true)
    private Date softCopyDeliveryDate;
    private String createBy;
    private Date createDate;
    private RdPaymentDTO payment;
    private List<RdContactPersonDTO> contactPersonList;
    private RdFlagsDTO flags;
    private RdOrderOthersDTO others;
    @ApiModelProperty(value = "customerList", dataType = "List", required = true)
    private List<RdCustomerDTO> customerList;
    @ApiModelProperty(value = "productList", dataType = "List", required = true)
    private List<RdProductDTO> productList;
    @ApiModelProperty(value = "sampleList", dataType = "List", required = true)
    private List<RdSampleDTO> sampleList;//TODO 待确认
    @ApiModelProperty(value = "serviceRequirement", dataType = "object", required = true)
    private RdServiceRequirementDTO serviceRequirement;
    // add 230922
    private List<RdTestItemMappingDTO> testItemMappingList;
    private List<RdAttachmentDTO> attachmentList;

    // add 20230529
    private List<RdTrfRelDTO> trfList;
    @ApiModelProperty(value = "orderInstanceId", dataType = "string", required = true)
    private String orderInstanceId;
    @ApiModelProperty(value = "lastModifiedTimestamp", dataType = "date", required = true)
    private Date lastModifiedTimestamp;
    @ApiModelProperty(value = "activeIndicator", dataType = "integer", required = true)
    private Integer activeIndicator;
    //SCI-1378
    private String topsLabId;
    private String topsLabCode;

    private RdRelationshipDTO relationship;
    private List<RdProcessListDTO> processList;

    @Data
    public static class RdRelationshipDTO {
        private RdRelationshipParentDTO parent;

        @Data
        public static class RdRelationshipParentDTO {
            private List<String> parcelNoList;
        }
    }
}
