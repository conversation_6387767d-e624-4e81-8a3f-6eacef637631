package com.sgs.testdatabiz.sdk;

import com.alibaba.fastjson.JSON;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import com.sgs.testdatabiz.facade.model.req.rd.ImportReportDataReq;
import com.sgs.testdatabiz.sdk.output.dto.RdOrderDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdQuotationDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdReportDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdTestSampleDTO;

import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


class RdConvertUtilTest {

    public static void main(String[] args)
    {
        try {
            File file = new File("C:\\Users\\<USER>\\work\\test.ini");
            String requestJson = readFile(file, "UTF-8");
            System.out.println("requestJson="+requestJson);
            String version = "v1";
            String env = "PROD";
            Integer updateVersion;
            //System.out.println("requestJson="+requestJson);
            String result = RdConvertUtil.convert(requestJson, version, env, 1,true,false);
            System.out.println("result="+result);
//            ImportReportDataReq importReportDataReq = JSON.parseObject(requestJson, ImportReportDataReq.class);
//            String req = JSON.toJSONString(importReportDataReq, true);

        }catch (Exception e) {
            e.printStackTrace();
        }

    }

//    public static void main(String[] args) {
//        try {
//            File file = new File("C:\\Users\\<USER>\\work\\test.txt");
//            String requestJson = readFile(file, "UTF-8");
//            ImportReportDataReq importReportDataReq = JSON.parseObject(requestJson, ImportReportDataReq.class);
//            Map<String, Object> result = new HashMap<>();
//            populateFields(result, importReportDataReq);
//            String jsonResult = JSON.toJSONString(result, true);
//            System.out.println(jsonResult);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    public static void populateFields(Map<String, Object> map, Object obj) throws IllegalAccessException {
        if (obj == null) {
            return;
        }
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(obj);
            if (value == null) {
                map.put(field.getName(), "");
            } else if (value instanceof List) {
                List<?> list = (List<?>) value;
                if (!list.isEmpty()) {
                    map.put(field.getName(), new ArrayList<>());
                    for (Object item : list) {
                        Map<String, Object> itemMap = new HashMap<>();
                        populateFields(itemMap, item);
                        ((List<Object>) map.get(field.getName())).add(itemMap);
                    }
                } else {
                    map.put(field.getName(), new ArrayList<>());
                }
            } else if (value instanceof ImportReportDataReq || value instanceof RdQuotationDTO || value instanceof RdInvoiceDTO || value instanceof RdConditionGroupDTO || value instanceof RdOrderDTO || value instanceof RdReportConclusionDTO || value instanceof RdReportDTO || value instanceof RdTestLineDTO || value instanceof RdTestResultDTO || value instanceof RdTestSampleDTO || value instanceof RdTrfDTO) {
                Map<String, Object> nestedMap = new HashMap<>();
                populateFields(nestedMap, value);
                map.put(field.getName(), nestedMap);
            } else {
                map.put(field.getName(), value);
            }
        }
    }


    public static String readFile(File file, String encoding) {
        try {
            byte[] buffer = new byte[(int) file.length()];
            FileInputStream fis = new FileInputStream(file);
            fis.read(buffer);
            fis.close();
            return new String(buffer, encoding);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}