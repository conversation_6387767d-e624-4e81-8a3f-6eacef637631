package com.sgs.testdatabiz.sdk.convert.impl;

import com.alibaba.fastjson.JSONObject;
import com.sgs.testdatabiz.sdk.input.dto.*;
import com.sgs.testdatabiz.sdk.output.dto.*;
import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import java.util.*;

/**
 * 测试新增字段的转换正确性
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-02
 */
public class FieldMappingTest {

    private AbstractDataConvertHandler convertHandler;
    private Date testDate;
    private String testUser;

    @Before
    public void setUp() {
        convertHandler = new DataConvertV1Handler();
        testDate = new Date();
        testUser = "test_user";
    }

    @Test
    public void testAttachmentFieldMapping() {
        // 准备测试数据 - RdAttachmentInput
        RdAttachmentInput attachmentInput = new RdAttachmentInput();
        attachmentInput.setFileName("test.pdf");
        attachmentInput.setFileType("PDF");
        attachmentInput.setCreatedBy(testUser);
        attachmentInput.setCreatedDate(testDate);
        attachmentInput.setActiveIndicator(1);

        // 创建测试输入数据
        ReportDataInput dataInput = new ReportDataInput();
        RdReportInput header = new RdReportInput();
        header.setReportFileList(Arrays.asList(attachmentInput));
        dataInput.setHeader(header);

        // 创建输出对象
        RdReportDataDTO reportDataDTO = new RdReportDataDTO();

        // 执行转换
        convertHandler.convertAttachmentList(dataInput, reportDataDTO);

        // 验证结果
        assertNotNull("附件列表不应为空", reportDataDTO.getAttachmentList());
        assertEquals("附件列表大小应为1", 1, reportDataDTO.getAttachmentList().size());
        
        RdAttachmentDTO attachmentDTO = reportDataDTO.getAttachmentList().get(0);
        assertEquals("创建者字段应正确映射", testUser, attachmentDTO.getCreatedBy());
        assertEquals("创建时间字段应正确映射", testDate, attachmentDTO.getCreatedDate());
        assertEquals("文件名应正确映射", "test.pdf", attachmentDTO.getFileName());
    }

    @Test
    public void testCustomerFieldMapping() {
        // 准备测试数据 - RdCustomerInput
        RdCustomerInput customerInput = new RdCustomerInput();
        customerInput.setCustomerName("Test Customer");
        customerInput.setCreatedDate(testDate);
        customerInput.setActiveIndicator(1);

        // 由于convertCustomerList是通过order.customerList调用的，这里简化测试
        // 只测试字段是否存在
        assertNotNull("CustomerInput应有createdDate字段", customerInput.getCreatedDate());
        assertEquals("createdDate应正确设置", testDate, customerInput.getCreatedDate());
    }

    @Test
    public void testTestSampleFieldMapping() {
        // 准备测试数据 - RdTestSampleInput
        RdTestSampleInput testSampleInput = new RdTestSampleInput();
        testSampleInput.setTestSampleNo("SAMPLE001");
        testSampleInput.setTestSampleName("Test Sample");
        testSampleInput.setCreatedBy(testUser);
        testSampleInput.setCreatedDate(testDate);
        testSampleInput.setActiveIndicator(1);

        List<RdTestSampleInput> testSampleList = Arrays.asList(testSampleInput);

        // 创建测试输入数据
        ReportDataInput dataInput = new ReportDataInput();
        dataInput.setTestSampleList(testSampleList);

        // 创建输出对象
        RdReportDataDTO reportDataDTO = new RdReportDataDTO();

        // 执行转换
        convertHandler.convertTestSampleList(dataInput, reportDataDTO);

        // 验证结果
        assertNotNull("测试样本列表不应为空", reportDataDTO.getTestSampleList());
        assertEquals("测试样本列表大小应为1", 1, reportDataDTO.getTestSampleList().size());
        
        RdTestSampleDTO testSampleDTO = reportDataDTO.getTestSampleList().get(0);
        assertEquals("创建者字段应正确映射", testUser, testSampleDTO.getCreatedBy());
        assertEquals("创建时间字段应正确映射", testDate, testSampleDTO.getCreatedDate());
        assertEquals("样本编号应正确映射", "SAMPLE001", testSampleDTO.getSampleNo());
    }

    @Test
    public void testQuotationFieldMapping() {
        // 准备测试数据 - RdQuotationInput
        RdQuotationInput quotationInput = new RdQuotationInput();
        quotationInput.setQuotationNo("QUO001");
        quotationInput.setSystemId(1);
        quotationInput.setCreatedBy(testUser);
        quotationInput.setCreatedDate(testDate);
        quotationInput.setActiveIndicator(1);

        // 添加服务项目（convertQuotation需要）
        RdServiceItemInput serviceItem = new RdServiceItemInput();
        serviceItem.setServiceItemName("Test Service");
        quotationInput.setServiceItemList(Arrays.asList(serviceItem));

        List<RdQuotationInput> quotationList = Arrays.asList(quotationInput);

        // 创建测试输入数据
        ReportDataInput dataInput = new ReportDataInput();
        dataInput.setQuotationList(quotationList);
        
        // 添加必要的order和header信息
        com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO order = 
            new com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO();
        order.setOrderNo("ORDER001");
        order.setRootOrderNo("ROOT001");
        dataInput.setOrder(order);
        
        RdReportInput header = new RdReportInput();
        header.setReportNo("RPT001");
        dataInput.setHeader(header);

        // 创建输出对象
        RdReportDataDTO reportDataDTO = new RdReportDataDTO();

        // 执行转换
        convertHandler.convertQuotation(dataInput, reportDataDTO);

        // 验证结果
        assertNotNull("报价单列表不应为空", reportDataDTO.getQuotationList());
        assertTrue("报价单列表应有至少一个元素", reportDataDTO.getQuotationList().size() > 0);
        
        RdQuotationDTO quotationDTO = reportDataDTO.getQuotationList().get(0);
        assertEquals("创建者字段应正确映射", testUser, quotationDTO.getCreatedBy());
        assertEquals("创建时间字段应正确映射", testDate, quotationDTO.getCreatedDate());
        assertEquals("报价单号应正确映射", "QUO001", quotationDTO.getQuotationNo());
    }

    @Test
    public void testTestResultFieldMapping() {
        // 准备测试数据 - RdTestResultInput
        RdTestResultInput testResultInput = new RdTestResultInput();
        testResultInput.setTestMatrixId("MATRIX001");
        testResultInput.setCreatedBy(testUser);
        testResultInput.setCreatedDate(testDate);
        testResultInput.setTestData("test data content");
        testResultInput.setActiveIndicator(1);

        List<RdTestResultInput> testResultList = Arrays.asList(testResultInput);

        // 创建测试输入数据
        ReportDataInput dataInput = new ReportDataInput();
        dataInput.setTestResultList(testResultList);
        dataInput.setLabId(1L);
        
        RdReportInput header = new RdReportInput();
        header.setOrderNo("ORDER001");
        header.setReportNo("RPT001");
        dataInput.setHeader(header);

        // 创建输出对象
        RdReportDataDTO reportDataDTO = new RdReportDataDTO();

        // 执行转换
        convertHandler.convertReportTestResultList(dataInput, reportDataDTO);

        // 验证结果
        assertNotNull("测试结果列表不应为空", reportDataDTO.getReportTestResultList());
        assertEquals("测试结果列表大小应为1", 1, reportDataDTO.getReportTestResultList().size());
        
        RdReportTestResultDTO testResultDTO = reportDataDTO.getReportTestResultList().get(0);
        assertEquals("创建者字段应正确映射", testUser, testResultDTO.getCreatedBy());
        assertEquals("创建时间字段应正确映射", testDate, testResultDTO.getCreatedDate());
        assertEquals("测试数据字段应正确映射", "test data content", testResultDTO.getTestData());
        assertEquals("测试矩阵ID应正确映射", "MATRIX001", testResultDTO.getTestMatrixId());
    }

    @Test
    public void testReportMatrixFieldMapping() {
        // 准备测试数据 - RdReportMatrixInput
        RdReportMatrixInput reportMatrixInput = new RdReportMatrixInput();
        reportMatrixInput.setTestMatrixId("MATRIX001");
        reportMatrixInput.setRdObjectRelId(12345L);
        reportMatrixInput.setCreatedBy(testUser);
        reportMatrixInput.setCreatedDate(testDate);
        reportMatrixInput.setActiveIndicator(1);

        List<RdReportMatrixInput> reportMatrixList = Arrays.asList(reportMatrixInput);

        // 创建测试输入数据
        ReportDataInput dataInput = new ReportDataInput();
        RdReportInput header = new RdReportInput();
        header.setReportMatrixList(reportMatrixList);
        header.setOrderNo("ORDER001");
        header.setReportNo("RPT001");
        dataInput.setHeader(header);
        dataInput.setLabId(1L);

        // 创建输出对象
        RdReportDataDTO reportDataDTO = new RdReportDataDTO();

        // 执行转换
        convertHandler.convertReportMatrixList(dataInput, reportDataDTO);

        // 验证结果
        assertNotNull("报告矩阵列表不应为空", reportDataDTO.getReportMatrixList());
        assertEquals("报告矩阵列表大小应为1", 1, reportDataDTO.getReportMatrixList().size());
        
        RdReportMatrixDTO reportMatrixDTO = reportDataDTO.getReportMatrixList().get(0);
        assertEquals("对象关联ID字段应正确映射", Long.valueOf(12345L), reportMatrixDTO.getRdObjectRelId());
        assertEquals("创建者字段应正确映射", testUser, reportMatrixDTO.getCreatedBy());
        assertEquals("创建时间字段应正确映射", testDate, reportMatrixDTO.getCreatedDate());
        assertEquals("测试矩阵ID应正确映射", "MATRIX001", reportMatrixDTO.getTestMatrixId());
    }

    @Test
    public void testFieldDefaultValues() {
        // 测试当输入字段为空时，是否有适当的默认值处理
        RdAttachmentInput attachmentInput = new RdAttachmentInput();
        attachmentInput.setFileName("test.pdf");
        // 不设置createdBy和createdDate

        ReportDataInput dataInput = new ReportDataInput();
        RdReportInput header = new RdReportInput();
        header.setReportFileList(Arrays.asList(attachmentInput));
        dataInput.setHeader(header);

        RdReportDataDTO reportDataDTO = new RdReportDataDTO();
        convertHandler.convertAttachmentList(dataInput, reportDataDTO);

        RdAttachmentDTO attachmentDTO = reportDataDTO.getAttachmentList().get(0);
        // 验证空值处理
        assertNull("未设置的创建者应为null", attachmentDTO.getCreatedBy());
        assertNull("未设置的创建时间应为null", attachmentDTO.getCreatedDate());
    }
}