package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdCustomerDTO extends BaseModel{
    private Long rdOrderId;
    private String customerInstanceId;
    private String orderNo;
    private String customerGroupCode;
    private String customerGroupName;
    private String contactPersonName;
    private String contactPersonEmail;
    private String contactPersonPhone;
    private String customerAddress;
    private String customerName;
    private Long bossNo;
    private Integer customerUsage;
    private Integer activeIndicator;
    private String marketSegmentCode;
    private String marketSegmentName;
    private String customerRefId;
    private List<RdCustomerLangDTO> customerLangList;

    private Date lastModifiedTimestamp;

    // tb_customer 表新增字段
    private Date createdDate;      // 创建时间

}
