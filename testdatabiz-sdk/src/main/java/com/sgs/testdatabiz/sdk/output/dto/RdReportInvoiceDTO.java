package com.sgs.testdatabiz.sdk.output.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:41
 */
@Data
public class RdReportInvoiceDTO extends BaseModel{

    private Long labId;
    private Long rdReportId;
    private Integer systemId;
    private String orderNo;
    private String rootOrderNo;
    private String reportNo;
    private String bossOrderNo;
    private String productCode;
    private String costCenter;
    private String projectTemplate;
    private Date invoiceDate;
    private String invoiceNo;
    private String currencyCode;
    private BigDecimal netAmount;
    private BigDecimal vatAmount;
    private BigDecimal totalAmount;
    private BigDecimal prePaidAmount;
    private Integer invoiceStatus;
    private String productCodeLabel;
    private String invoiceStatusLabel;
    private Integer activeIndicator;
    private List<String> quotationNos;

    private String sourceSystem;

    private Date lastModifiedTimestamp;

    private String invoiceInstanceId;

    // tb_report_invoice 表新增字段
    private Date createdDate;      // 创建时间

}
