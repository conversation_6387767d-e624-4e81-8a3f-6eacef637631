# TestDataBiz SDK 字段映射分析报告

基于data.md中的表字段信息，以下是SDK中缺失字段的详细分析：

## 1. tb_attachment 表字段缺失分析

### 数据库表字段:
- created_by (VARCHAR)
- created_date (DATETIME)

### 当前DTO状态:
- **RdAttachmentInput**: ❌ 缺失 created_by, created_date
- **RdAttachmentDTO**: ❌ 缺失 created_by, created_date

### 需要添加的字段:
```java
// RdAttachmentInput & RdAttachmentDTO
private String createdBy;      // 创建者
private Date createdDate;      // 创建时间
```

## 2. tb_customer 表字段缺失分析

### 数据库表字段:
- created_date (DATETIME)

### 当前DTO状态:
- **RdCustomerInput**: ❌ 缺失 created_date
- **RdCustomerDTO**: ❌ 缺失 created_date

### 需要添加的字段:
```java
// RdCustomerInput & RdCustomerDTO
private Date createdDate;      // 创建时间
```

## 3. tb_customer_lang 表字段缺失分析

### 数据库表字段:
- language_id (INT)
- customer_name (VARCHAR)
- customer_address (TEXT)

### 当前DTO状态:
- **RdCustomerLangDTO**: ✅ 所有字段已存在

## 4. tb_quotation 表字段缺失分析

### 数据库表字段:
- created_by (VARCHAR)
- created_date (DATETIME)

### 当前DTO状态:
- **RdQuotationInput**: ❌ 缺失 created_by, created_date
- **RdQuotationDTO**: ❌ 缺失 created_by, created_date

### 需要添加的字段:
```java
// RdQuotationInput & RdQuotationDTO
private String createdBy;      // 创建者
private Date createdDate;      // 创建时间
```

## 5. tb_quotation_lang 表字段缺失分析

### 数据库表字段:
- language_id (INT)
- payer_customer_name (VARCHAR)
- citation_name (VARCHAR)
- citation_full_name (VARCHAR)
- service_item_name (VARCHAR)

### 当前DTO状态:
- **RdQuotationLangDTO**: ✅ 所有字段已存在

## 6. tb_report_conclusion 表字段缺失分析

### 数据库表字段:
- created_date (DATETIME)

### 当前DTO状态:
- **RdReportConclusionDTO**: ❌ 缺失 created_date

### 需要添加的字段:
```java
// RdReportConclusionDTO
private Date createdDate;      // 创建时间
```

## 7. tb_report_invoice 表字段缺失分析

### 数据库表字段:
- created_date (DATETIME)

### 当前DTO状态:
- **RdReportInvoiceDTO**: ❌ 缺失 created_date

### 需要添加的字段:
```java
// RdReportInvoiceDTO
private Date createdDate;      // 创建时间
```

## 8. tb_report_ext 表字段缺失分析

### 数据库表字段:
- request_json (TEXT)

### 当前DTO状态:
- **RdReportExtDTO**: ✅ request_json 字段已存在

## 9. tb_report_matrix 表字段缺失分析

### 数据库表字段:
- rd_object_rel_id (BIGINT)
- created_by (VARCHAR)
- created_date (DATETIME)

### 当前DTO状态:
- **RdReportMatrixInput**: ❌ 缺失 rd_object_rel_id, created_by, created_date
- **RdReportMatrixDTO**: ❌ 缺失 rd_object_rel_id, created_by, created_date

### 需要添加的字段:
```java
// RdReportMatrixInput & RdReportMatrixDTO
private Long rdObjectRelId;    // 对象关联ID
private String createdBy;      // 创建者
private Date createdDate;      // 创建时间
```

## 10. tb_report_test_matrix_pp 表字段缺失分析

### 数据库表字段:
- test_matrix_id (VARCHAR)
- pp_no (VARCHAR)
- pp_name (VARCHAR)

### 当前DTO状态:
- **无对应的DTO类**: ❌ 需要创建新的DTO类

### 需要创建的DTO:
```java
// 新建 RdReportTestMatrixPpDTO
public class RdReportTestMatrixPpDTO extends BaseModel {
    private String testMatrixId;
    private String ppNo;
    private String ppName;
}
```

## 11. tb_report_matrix_lang 表字段缺失分析

### 数据库表字段:
- rd_report_matrix_id (BIGINT)
- language_id (INT)
- evaluation_alias (VARCHAR)
- evaluation_name (VARCHAR)
- citation_name (VARCHAR)
- citation_full_name (VARCHAR)
- method_desc (TEXT)
- customer_conclusion (TEXT)

### 当前DTO状态:
- **RdReportMatrixLangDTO**: ❌ 缺失多个字段

### 需要添加的字段:
```java
// RdReportMatrixLangDTO
private Long rdReportMatrixId;        // 报告矩阵ID
private Integer languageId;           // 语言ID
private String evaluationAlias;       // 评估别名
private String evaluationName;        // 评估名称
private String citationName;          // 引用名称
private String citationFullName;      // 引用全名
private String methodDesc;            // 方法描述
private String customerConclusion;    // 客户结论
```

## 12. tb_report_test_result 表字段缺失分析

### 数据库表字段:
- created_by (VARCHAR)
- created_date (DATETIME)
- test_data (TEXT)

### 当前DTO状态:
- **RdReportTestResultDTO**: ❌ 缺失 created_by, created_date, test_data

### 需要添加的字段:
```java
// RdReportTestResultDTO
private String createdBy;      // 创建者
private Date createdDate;      // 创建时间
private String testData;       // 测试数据
```

## 13. tb_report_test_result_lang 表字段缺失分析

### 数据库表字段:
- rd_report_test_result_id (BIGINT)
- language_id (INT)
- test_result_full_name (VARCHAR)
- result_value_remark (VARCHAR)
- result_unit (VARCHAR)
- limit_value_full_name (VARCHAR)
- limit_unit (VARCHAR)

### 当前DTO状态:
- **RdReportTestResultLangDTO**: ❌ 缺失多个字段

### 需要添加的字段:
```java
// RdReportTestResultLangDTO
private Long rdReportTestResultId;    // 测试结果ID
private Integer languageId;           // 语言ID
private String testResultFullName;    // 测试结果全名
private String resultValueRemark;     // 结果值备注
private String resultUnit;            // 结果单位
private String limitValueFullName;    // 限值全名
private String limitUnit;             // 限值单位
```

## 14. tb_test_data_object_rel 表字段缺失分析 ⚠️ 重点

### 数据库表字段:
- ProductLineCode (VARCHAR)
- LabCode (VARCHAR)
- OrderNo (VARCHAR)
- ParentOrderNo (VARCHAR)
- ReportNo (VARCHAR)
- ObjectNo (VARCHAR)
- ExternalNo (VARCHAR)
- ExternalObjectNo (VARCHAR)
- LanguageId (INT)
- CompleteDate (DATETIME)
- ActiveIndicator (TINYINT)
- CreatedDate (DATETIME)
- source_type_label (VARCHAR)

### 当前DTO状态:
- **RdTestDataObjectRelDTO**: ✅ 已存在部分字段，❌ 缺失 CreatedDate, source_type_label

### 需要添加的字段:
```java
// RdTestDataObjectRelDTO
private Date createdDate;         // 创建时间 - 缺失
// source_type_label 已作为 sourceTypeLabel 存在
```

## 15. tb_test_sample 表字段缺失分析

### 数据库表字段:
- created_by (VARCHAR)
- created_date (DATETIME)

### 当前DTO状态:
- **RdTestSampleInput**: ❌ 缺失 created_by, created_date
- **RdTestSampleDTO**: ❌ 缺失 created_by, created_date

### 需要添加的字段:
```java
// RdTestSampleInput & RdTestSampleDTO
private String createdBy;      // 创建者
private Date createdDate;      // 创建时间
```

## 16. tb_test_sample_group 表字段缺失分析

### 数据库表字段:
- created_date (DATETIME)

### 当前DTO状态:
- **RdTestSampleGroupDTO**: ❌ 缺失 created_date

### 需要添加的字段:
```java
// RdTestSampleGroupDTO
private Date createdDate;      // 创建时间
```

## 17. tb_report_certificate 表字段缺失分析

### 数据库表字段:
- active_indicator (TINYINT)

### 当前DTO状态:
- **无对应的DTO类**: ❌ 需要创建新的DTO类

### 需要创建的DTO:
```java
// 新建 RdReportCertificateDTO
public class RdReportCertificateDTO extends BaseModel {
    private Integer activeIndicator;
}
```

## 汇总统计

| 表名 | 缺失字段数量 | 主要缺失字段类型 | 优先级 |
|------|-------------|-----------------|-------|
| tb_attachment | 2 | 审计字段 | 高 |
| tb_customer | 1 | 审计字段 | 高 |
| tb_quotation | 2 | 审计字段 | 高 |
| tb_report_matrix | 3 | 关联ID + 审计字段 | 高 |
| tb_report_test_result | 3 | 审计字段 + 数据字段 | 高 |
| tb_test_data_object_rel | 1 | 审计字段 | 中 |
| tb_test_sample | 2 | 审计字段 | 中 |
| tb_test_sample_group | 1 | 审计字段 | 中 |
| tb_report_conclusion | 1 | 审计字段 | 中 |
| tb_report_invoice | 1 | 审计字段 | 中 |

## 实施建议

1. **第一批**: 优先处理高频使用的审计字段 (created_by, created_date)
2. **第二批**: 处理关联ID字段 (rd_object_rel_id等)
3. **第三批**: 处理业务数据字段 (test_data等)
4. **第四批**: 创建新的DTO类并建立映射关系