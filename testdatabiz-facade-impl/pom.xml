<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sgs-testdatabiz</artifactId>
        <groupId>com.sgs.testdatabiz</groupId>
        <version>1.3.48</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${testdatabiz.version}</version>
    <artifactId>testdatabiz-facade-impl</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sgs.testdatabiz</groupId>
            <artifactId>testdatabiz-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.15</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-extension-starter</artifactId>
            <version>4.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.sgs.testdatabiz</groupId>
            <artifactId>testdatabiz-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.testdatabiz</groupId>
            <artifactId>testdatabiz-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
