package com.sgs.testdatabiz.facade.impl;

import com.alibaba.fastjson.JSON;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.testdata.TestDataHandler;
import com.sgs.testdatabiz.domain.service.testdata.factory.TestDataHandlerFactory;
import com.sgs.testdatabiz.facade.model.req.starlims.ReceiveStarLimsReportReq;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.slim.SlimJobInfo;
import com.sgs.testdatabiz.facade.v2.ReportTestDataFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 报告检测数据facade实现
 * @author: shawn.yang
 * @create: 2023-03-14 10:35
 */
@Service("reportTestDataFacadeImpl")
@Slf4j
public class ReportTestDataFacadeImpl implements ReportTestDataFacade {


    private final TestDataHandlerFactory handlerFactory;



    @Override
    public BaseResponse<ReportTestDataInfo> build4Slim(String rawDataJson) {
        // translate rawData to SlimProJobInfo
        SlimJobInfo slimProJobInfo = JSON.parseObject(rawDataJson, SlimJobInfo.class);

        // buildReportTestDataInfo
        return this.buildReportTestDataInfo(slimProJobInfo, SourceTypeEnum.SLIM);
    }


    @Override
    public BaseResponse<ReportTestDataInfo> build4StarLims(String rawDataJson) {
        if (StringUtils.isBlank(rawDataJson)){
            return BaseResponse.newFailInstance(ResponseCode.ILLEGAL_ARGUMENT);
        }

        String jsonString = JSON.toJSONString(rawDataJson);
        log.info(jsonString);
        // translate rawData to ReceiveStarLimsReportReq
        ReceiveStarLimsReportReq receiveStarLimsReportReq = JSON.parseObject(rawDataJson, ReceiveStarLimsReportReq.class);

        // buildReportTestDataInfo
        return this.buildReportTestDataInfo(receiveStarLimsReportReq, SourceTypeEnum.STARLIMS);
    }




    private <Input> BaseResponse<ReportTestDataInfo> buildReportTestDataInfo(Input data, SourceTypeEnum channel){
        // 1.通过工厂获取Handler
        TestDataHandler<Input> handler = handlerFactory.getHandler(channel);

        // 2.build ReportTestDataInfo
        return handler.buildData(data);
    }


    public ReportTestDataFacadeImpl(TestDataHandlerFactory handlerFactory) {
        this.handlerFactory = handlerFactory;
    }

}
