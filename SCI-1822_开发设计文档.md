# SCI-1822 TestDataBiz SDK 数据字段完善开发设计文档

## 1. 任务概述

### 1.1 JIRA信息
- **任务编号**: SCI-1822
- **任务标题**: TestDataBiz SDK 数据字段完善
- **任务类型**: 功能完善
- **优先级**: 中等
- **创建时间**: 2025年09月

### 1.2 问题描述
TestDataBiz SDK中的数据传输对象（DTO）缺少部分数据库表字段，导致数据转换时信息丢失，影响数据完整性。需要根据data.md中定义的数据库表字段，完善SDK中Input和Output DTO对象。

### 1.3 解决目标
- 补充SDK中缺失的数据库字段映射
- 确保数据转换过程中信息完整性
- 维护向后兼容性
- 提升数据转换的准确性

### 1.4 业务价值
- **数据完整性**: 确保所有数据库字段在SDK转换过程中不丢失
- **映射准确性**: 建立完整的字段映射关系
- **向后兼容**: 保证现有功能不受影响
- **可维护性**: 为未来新增字段提供标准化处理方式

## 2. 需求分析

### 2.1 当前状况分析
通过对data.md中数据库表字段与现有SDK DTO对象的对比分析，发现以下缺失：

| 数据库表 | 缺失字段数量 | 主要缺失字段类型 | 影响程度 |
|----------|-------------|-----------------|----------|
| tb_attachment | 2 | 审计字段(created_by, created_date) | 高 |
| tb_customer | 1 | 审计字段(created_date) | 高 |
| tb_quotation | 2 | 审计字段(created_by, created_date) | 高 |
| tb_report_matrix | 3 | 关联ID + 审计字段 | 高 |
| tb_report_test_result | 3 | 审计字段 + 业务数据字段 | 高 |
| tb_test_data_object_rel | 1 | 审计字段(created_date) | 中 |
| tb_test_sample | 2 | 审计字段(created_by, created_date) | 中 |
| tb_test_sample_group | 1 | 审计字段(created_date) | 中 |
| tb_report_conclusion | 1 | 审计字段(created_date) | 中 |
| tb_report_invoice | 1 | 审计字段(created_date) | 中 |

### 2.2 核心缺失字段分类

#### 2.2.1 审计字段
- `created_by` (VARCHAR): 创建者信息
- `created_date` (DATETIME): 创建时间
- 影响：数据溯源、审计功能受限

#### 2.2.2 关联字段
- `rd_object_rel_id` (BIGINT): 对象关联ID
- 影响：数据关联关系不完整

#### 2.2.3 业务数据字段
- `test_data` (TEXT): 测试数据
- 影响：业务数据丢失

## 3. 技术方案

### 3.1 架构设计

```mermaid
graph TB
    A[外部系统数据] -->|JSON输入| B[RdConvertUtil]
    B --> C[AbstractDataConvertHandler]
    C --> D[Input DTO Objects]
    D --> E[字段映射转换]
    E --> F[Output DTO Objects]
    F --> G[RdReportDataDTO]
    
    subgraph "新增字段处理"
        H[created_by映射]
        I[created_date映射]
        J[关联ID映射]
        K[业务数据映射]
    end
    
    E --> H
    E --> I
    E --> J
    E --> K
```

### 3.2 字段映射策略

#### 3.2.1 命名规范
| 数据库字段 | Java字段 | 数据类型 | 转换规则 |
|------------|----------|----------|----------|
| created_by | createdBy | String | 下划线转驼峰 |
| created_date | createdDate | Date | 下划线转驼峰，时间类型 |
| rd_object_rel_id | rdObjectRelId | Long | 下划线转驼峰，长整型 |
| test_data | testData | String | 下划线转驼峰 |

#### 3.2.2 默认值策略
- `createdDate`: 输入为空时使用当前时间 `new Date()`
- `createdBy`: 输入为空时保持 `null`
- 其他字段: 保持现有空值处理逻辑

### 3.3 实施范围

#### 3.3.1 需要修改的Input DTO类
1. **RdAttachmentInput**: 新增 createdBy, createdDate
2. **RdCustomerInput**: 新增 createdDate
3. **RdQuotationInput**: 新增 createdBy, createdDate
4. **RdReportMatrixInput**: 新增 rdObjectRelId, createdBy, createdDate
5. **RdTestResultInput**: 新增 createdBy, createdDate, testData
6. **RdTestSampleInput**: 新增 createdBy, createdDate

#### 3.3.2 需要修改的Output DTO类
1. **RdAttachmentDTO**: 新增 createdBy, createdDate
2. **RdCustomerDTO**: 新增 createdDate
3. **RdQuotationDTO**: 新增 createdBy, createdDate
4. **RdReportMatrixDTO**: 新增 rdObjectRelId, createdBy, createdDate
5. **RdReportTestResultDTO**: 新增 createdBy, createdDate, testData
6. **RdTestSampleDTO**: 新增 createdBy, createdDate
7. **RdTestSampleGroupDTO**: 新增 createdDate
8. **RdReportConclusionDTO**: 新增 createdDate
9. **RdReportInvoiceDTO**: 新增 createdDate
10. **RdTestDataObjectRelDTO**: 新增 createdDate

#### 3.3.3 需要修改的转换方法
1. **convertAttachmentList()**: 添加附件字段映射
2. **convertCustomerList()**: 添加客户字段映射
3. **convertQuotation()**: 添加报价单字段映射
4. **convertReportMatrixList()**: 添加报告矩阵字段映射
5. **setReportTestResultInfo()**: 添加测试结果字段映射
6. **convertTestSampleList()**: 添加测试样本字段映射
7. **convertReportConclusionList()**: 添加结论字段映射
8. **convertReportInvoiceList()**: 添加发票字段映射

## 4. 详细设计

### 4.1 DTO字段添加示例

#### 4.1.1 RdAttachmentInput/DTO
```java
public class RdAttachmentInput extends BaseModel {
    // ... existing fields ...
    
    /** 创建者 */
    private String createdBy;
    
    /** 创建时间 */
    private Date createdDate;
    
    // getter and setter methods
}
```

#### 4.1.2 RdReportMatrixInput/DTO
```java
public class RdReportMatrixInput extends BaseModel {
    // ... existing fields ...
    
    /** 对象关联ID */
    private Long rdObjectRelId;
    
    /** 创建者 */
    private String createdBy;
    
    /** 创建时间 */
    private Date createdDate;
    
    // getter and setter methods
}
```

### 4.2 转换逻辑实现示例

#### 4.2.1 附件转换逻辑
```java
protected void convertAttachmentList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
    // ... existing logic ...
    
    rdAttachmentDTOS.forEach(attachment -> {
        // 设置创建者信息
        if (StringUtils.isNotBlank(attachmentInput.getCreatedBy())) {
            attachment.setCreatedBy(attachmentInput.getCreatedBy());
        }
        
        // 设置创建时间
        if (attachmentInput.getCreatedDate() != null) {
            attachment.setCreatedDate(attachmentInput.getCreatedDate());
        } else {
            attachment.setCreatedDate(new Date()); // 默认当前时间
        }
    });
}
```

### 4.3 数据类型映射表

| 数据库类型 | Java类型 | 描述 | 示例 |
|-----------|---------|------|------|
| VARCHAR | String | 字符串类型 | created_by |
| DATETIME | Date | 时间类型 | created_date |
| BIGINT | Long | 长整型 | rd_object_rel_id |
| INT | Integer | 整型 | language_id |
| TEXT | String | 大文本类型 | test_data |
| TINYINT | Integer | 小整型 | active_indicator |

## 5. 实施计划

### 5.1 开发阶段

| 阶段 | 任务描述 | 涉及文件 | 预估工时 | 风险等级 |
|------|----------|----------|----------|----------|
| Phase 1 | DTO字段补充 | Input/Output DTO类 | 1人日 | 低 |
| Phase 2 | 转换逻辑更新 | AbstractDataConvertHandler等 | 1.5人日 | 中 |
| Phase 3 | 单元测试编写 | 测试类 | 1人日 | 低 |
| Phase 4 | 集成测试验证 | 完整转换流程 | 0.5人日 | 中 |
| Phase 5 | 文档更新 | README, API文档 | 0.5人日 | 低 |

### 5.2 关键里程碑

- **M1**: 所有DTO字段补充完成 (Day 1)
- **M2**: 转换逻辑实现完成 (Day 2.5)
- **M3**: 单元测试通过 (Day 3.5)
- **M4**: 集成测试通过 (Day 4)
- **M5**: 代码提测 (Day 4.5)

## 6. 风险分析与缓解

### 6.1 技术风险

| 风险项 | 风险描述 | 概率 | 影响 | 缓解措施 |
|--------|----------|------|------|----------|
| 向后兼容性 | 新增字段影响现有功能 | 低 | 高 | 设计为可选字段，保留默认值逻辑 |
| 性能影响 | 字段增加导致转换性能下降 | 低 | 中 | 增量添加，分批验证性能 |
| 数据类型转换 | 类型映射错误 | 中 | 中 | 严格按照数据库定义进行映射 |
| 空值处理 | 空值导致转换异常 | 中 | 中 | 完善空值检查和默认值处理 |

### 6.2 业务风险

| 风险项 | 风险描述 | 概率 | 影响 | 缓解措施 |
|--------|----------|------|------|----------|
| 数据丢失 | 新增字段映射错误导致数据丢失 | 低 | 高 | 充分的单元测试和集成测试 |
| 业务中断 | 修改影响现有业务流程 | 低 | 高 | 分阶段部署，保留回滚方案 |

## 7. 质量保证

### 7.1 代码质量
- 遵循项目编码规范
- 完善的注释和文档
- 代码审查机制

### 7.2 测试覆盖
- 单元测试覆盖率 ≥ 85%
- 集成测试覆盖主要业务场景
- 性能测试验证无明显性能下降

### 7.3 部署质量
- 灰度发布机制
- 监控和告警
- 快速回滚能力

## 8. 验收标准

### 8.1 功能验收
- [ ] 所有缺失字段已添加到对应DTO类
- [ ] 字段映射逻辑正确实现
- [ ] 转换结果包含完整的字段信息
- [ ] 向后兼容性验证通过

### 8.2 质量验收
- [ ] 单元测试通过率 100%
- [ ] 代码覆盖率 ≥ 85%
- [ ] 性能测试无明显衰退
- [ ] 代码审查通过

### 8.3 文档验收
- [ ] 技术文档更新完成
- [ ] 测试文档编写完成
- [ ] 部署文档更新完成

## 9. 交付物清单

### 9.1 代码交付物
- 修改的Input DTO类 (6个文件)
- 修改的Output DTO类 (10个文件)
- 修改的转换逻辑类 (1个文件，8个方法)
- 新增的单元测试类 (1个文件)

### 9.2 文档交付物
- 开发设计文档 (本文档)
- 测试用例文档
- 字段映射对照表
- 部署说明文档

### 9.3 测试交付物
- 单元测试报告
- 集成测试报告
- 性能测试报告
- 兼容性测试报告

## 10. 附录

### 10.1 参考文档
- data.md - 数据库表字段定义
- field-mapping-analysis.md - 字段映射分析
- field-completion-report.md - 实施记录

### 10.2 相关JIRA
- SCI-1822 - TestDataBiz SDK 数据字段完善

### 10.3 技术联系人
- 开发负责人: [开发人员姓名]
- 测试负责人: [测试人员姓名]
- 产品负责人: [产品人员姓名]