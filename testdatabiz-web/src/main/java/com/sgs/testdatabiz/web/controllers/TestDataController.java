package com.sgs.testdatabiz.web.controllers;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.domain.service.TestDataService;
import com.sgs.testdatabiz.facade.FastFacade;
import com.sgs.testdatabiz.facade.IConclusionFacade;
import com.sgs.testdatabiz.facade.TestDataFacade;
import com.sgs.testdatabiz.facade.model.req.ConclusionInfoReq;
import com.sgs.testdatabiz.facade.model.req.TestDataDeleteReq;
import com.sgs.testdatabiz.facade.model.req.TestDataQueryReq;
import com.sgs.testdatabiz.facade.model.req.config.ConfigReq;
import com.sgs.testdatabiz.facade.model.req.fast.TestDataInfoReq;
import com.sgs.testdatabiz.facade.model.rsp.ConclusionInfoRsp;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixRsp;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.slim.SlimProJobInfo;
import com.sgs.testdatabiz.facade.v2.ReportTestDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/testdata")
@Api(value = "/testdata", tags = "testdata")
public class TestDataController {
    private static final Logger logger = LoggerFactory.getLogger(TestDataController.class);
    @Autowired
    private TestDataFacade testDataFacade;
    @Autowired
    private TestDataService testDataService;
    @Autowired
    private IConclusionFacade conclusionFacade;
    @Autowired
    private FastFacade fastFacade;
    @Autowired
    private ReportTestDataService reportTestDataService;
    /**
     * 保存Slim Test Data数据
     *
     * @param reqObject
     * @return
     */
    @PostMapping("/saveSlimTestData")
    @ApiOperation(value = "保存Slim Test Data数据")
    public BaseResponse saveSlimTestData(@RequestBody SlimProJobInfo reqObject) {
        return testDataFacade.slimTestData(reqObject);
    }
    @PostMapping("/saveFastTestData")
    public BaseResponse saveFastTestData(TestDataInfoReq reqObject){
        return fastFacade.fastSaveTestData(reqObject);
    }
    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/queryTestData")
    @ApiOperation(value = "获取TestData数据")
    public BaseResponse queryTestData(@RequestBody TestDataQueryReq reqObject) {
        return testDataFacade.queryTestData(reqObject);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/deleteTestData")
    @ApiOperation(value = "删除TestData数据")
    public BaseResponse deleteTestData(@RequestBody TestDataDeleteReq reqObject) {
        return testDataFacade.deleteTestData(reqObject);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/saveTestData")
    @ApiOperation(value = "保存TestData数据")
    public BaseResponse saveTestData(@RequestBody ReportTestDataInfo reqObject) {
        return reportTestDataService.importData(reqObject);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/cleanHistoryData")
    @ApiOperation(value = "删除TestData数据")
    public BaseResponse cleanHistoryData(@RequestBody ConfigReq reqObject) {
        return BaseResponse.newInstance(testDataService.cleanHistoryData(reqObject));
    }

    @PostMapping("/getTestDataInfoList")
    @ApiOperation(value = "提供给外部调用，获取testData数据")
    public BaseResponse<List<TestDataTestMatrixRsp>> getTestDataInfoList(@RequestBody TestDataQueryReq reqObject) {
        return testDataFacade.getTestDataInfoList(reqObject);
    }

    @PostMapping("/queryAllConclusionList")
    @ApiOperation(value = "提供给外部调用，获取分包数据conclusion数据")
    public BaseResponse queryAllConclusionList(@RequestBody ConclusionInfoReq reqObject) {
        BaseResponse<List<ConclusionInfoRsp>> allConclusionInfoList = conclusionFacade.getAllConclusionInfoList(reqObject);
        return allConclusionInfoList;
    }

}
